import json
import asyncio
import uuid
from med_config import CALL_CONFIG, LLM_CONFIG, get_system_prompt, get_initial_chat_history
from reply_cache import SingleDocCacheManager
from rpc_utils import RPCUtils
from llm_client import LLMClient
from tools.str_utils import smart_extract_json, extract_json_fallback2, ends_comma, remove_last_comma, \
    split_chinese_text
from log_utils import debug, info, warning, error, filter_print_xcc_events
import json_repair

class AIResponse:
    def __init__(self, rpc_utils: RPCUtils, person_info=None):
        self.rpc_utils = rpc_utils
        self.ctrl_uuid = self.rpc_utils.ctrl_uuid
        self.person_info = person_info

        # 初始化LLM客户端
        person_name = self.person_info.get("name", "患者")
        system_prompt = get_system_prompt(person_name)

        self.llm_client = LLMClient(LLM_CONFIG, system_prompt, auto_update_chat=False)
        self.llm_client.chat_history = get_initial_chat_history(person_name)
        self.init_response = f"您好，我是武汉协和医院骨科的智能助理，请问是{person_name}吗？"

        self.doc_cache  = SingleDocCacheManager()

    async def init(self):
        await self.doc_cache.get_cache_doc()

    async def get_cache_reply(self, user_text):
        if len(self.llm_client.chat_history) > 0:
            last_question = self.llm_client.chat_history[-1]['content']
            # 模板中没有处理语音识别用户文字的标点，讯飞给出的识别结果通常是句号结尾
            user_text = await remove_last_comma(user_text)
            cache_reply = await self.doc_cache.find_cached_response(last_question, user_text)
            return cache_reply
        return None


    async def get_response(self, user_text):
        cache_reply = await self.get_cache_reply(user_text)
        if cache_reply is not None:
            return cache_reply, 1
        try:
            llm_response = await self.llm_client.get_response(user_text)
            if llm_response.success:
                llm_reply = llm_response.content

                return llm_reply, 0
            else:
                error(f"LLM调用失败: {llm_response.error_message}")
                # 如果LLM调用失败，返回默认回复
                fallback_reply = "抱歉，智能系统有点问题，再见"
                return fallback_reply, -1
        except Exception as e:
            error(f"调用LLM时发生异常: {str(e)}")
            # 异常情况下的默认回复
            fallback_reply = "抱歉，系统暂时有点问题，再见"
            return fallback_reply, -1

    async def get_response_stream(self, user_text):
        cache_reply = await self.get_cache_reply(user_text)
        if cache_reply is not None:
            info(f"命中缓存: {cache_reply}")
            for  sent  in await split_chinese_text(cache_reply):
                yield sent, 1 # 1表示缓存返回
            return


        acc_chunk = '' # 累积到有标点符号再yield 否则tts播放卡顿
        i = 0
        async for chunk in self.llm_client.get_response_stream(user_text):
            if chunk.success:
                acc_chunk += chunk.content
                if i == -1: # 第一个chunk直接返回，快速TTS响应
                    yield acc_chunk, 0
                    acc_chunk = ''
                else:
                    if await ends_comma(acc_chunk) == True:
                        if len(acc_chunk) == 1:# 避免只发送一个标点符号
                            continue
                        yield acc_chunk, 0
                        acc_chunk = ''
            else:
                yield "抱歉，系统暂时有点问题，再见", -1
                break
            i += 1

        if acc_chunk != '':
            yield acc_chunk, 0

    async def init_session(self):
        await self.llm_client.init_session()

    async def release_session(self):
        """清理资源"""
        try:
            await self.llm_client.close()
        except Exception as e:
            warning(f"清理LLM客户端时出错: {e}")


async def format_json_pretty(json_data, indent=4):
    """格式化JSON数据，类似pprint的效果"""
    if isinstance(json_data, str):
        data = json.loads(json_data)
    else:
        data = json_data

    # 创建格式化的文本
    formatted_text = "患者回访数据报告\n"
    formatted_text += "=" * 50 + "\n\n"

    # 为每个键值对创建缩进格式
    for key, value in data.items():
        # 处理多行内容
        if "\n" in str(value):
            value_lines = str(value).split("\n")
            formatted_text += f"{key}:\n"
            for line in value_lines:
                formatted_text += " " * indent + line + "\n"
        else:
            formatted_text += f"{key}:\n{' ' * indent}{value}\n"
        formatted_text += "\n"

    title = '患者:' + data['患者名字'] + ' 今天摆腿不适感:' + data['不适感内容']
    return title, formatted_text


async def extract_conversation_info(chat_list, llm_client: LLMClient):
    """从对话历史中提取重要信息"""
    # 构建对话历史文本
    chat_history = ""
    for conv in chat_list:
        role = "医生" if conv["role"] == "assistant" else "患者"
        chat_history += f"[{role}]: {conv['content']}\n"

    # 构建提取信息的提示词
    data_template = {
        '训练完成情况': '完成/未完成',
        '训练未完成原因': '未完成的原因',
        '训练次数': 'xx次',
        '训练时长': 'xx分钟',
        '是否有不适感': '是/否',
        '不适感内容': 'xxx',
        '不适感锻炼结束后是否恢复': '是/否',
        '是否需要医生人工和患者联系': '是/否',
        '锻炼辅助仪器是否有问题': '是/否',
        '锻炼辅助仪器问题内容': 'xxxx'
    }

    prompt = '''
    请你提取以下对话中的重要信息，输出为json格式，
    主要提取患者的情况，请根据样例属性进行抽取，不要漏掉属性，如果提取结果中数字都使用阿拉伯数字
    
    请将你的JSON响应包装在<output></output>标签中，提取样例如下:：
    <output>
    {json}
    </output>
    确保输出的JSON格式正确，可以被Python的json.loads()解析。
    
    以下是对话历史
    ```
    {chat_history}
    ```
    '''.format(json=json.dumps(data_template, ensure_ascii=False), chat_history=chat_history)

    # 调用LLM提取信息
    response = await llm_client.get_response(prompt)

    # 处理响应
    if response.success:
        response_content_str = response.content
        info(f'response_content_str:{response_content_str}')
        # 处理响应
        # ind = response_content.rfind('```')
        # if ind != -1:
        #     response_content = response_content[:ind]
        # response_content = response_content.replace('`', '')
        # response_content = json.loads(response_content)

        response_content = await smart_extract_json(response_content_str)
        if response_content is None:
            response_content = await extract_json_fallback2(response_content_str)
            response_content = json_repair.repair_json(response_content, return_objects=True)

        # 格式化信息
        # title, formatted_text = await format_json_pretty(response_content)

        return response_content
    else:
        # 处理LLM调用失败的情况
        return None

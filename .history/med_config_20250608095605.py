"""
配置文件
"""

# NATS服务器配置
NATS_CONFIG = {
    "url": "nats://tycy:<EMAIL>:4222"
}

# 电话配置
CALL_CONFIG = {
    "service": "cn.xswitch.node.test",
    "dial_string": "sofia/gateway/out/tyscg18971492577",  # 默认拨号字符串
    "timeout": 40  # 拨号超时时间（秒）
}

# TTS配置
TTS_CONFIG = {
    "engine": "xunfei",
    "voice": "x4_yezi"
}

# ASR配置（连续识别）
ASR_CONFIG = {
    "engine": "ali",
    "grammar": "default",
    "no_input_timeout": 15000,
    "speech_timeout": 18000,
    "partial_events": True,
    "disable_detected_data_event": False,  # 改为False以启用语音识别事件
    "nobreak": False,
    "break_delay": 2000,
    "params": {
        "vad-silence-ms": "600",  # 添加语音活动检测
        "voice-ms": "500",
        "energy-level": "300",     # 添加能量级别阈值
        "segment-recording-prefix": "/usr/local/freeswitch/storage/recordings/segment-recording-2024-",
        "silence-ms": "700",# 静音时间（1秒）
		"add-punc": "true",
        "vad-mode": "-1",
        "vad-debug": "1",
        # "engine_model_type":"8k_zh"
    }
}

# 日志配置
LOG_CONFIG = {
    "level": "DEBUG",           # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
    "use_colors": True,        # 是否使用颜色输出
    "use_icons": True,         # 是否使用图标
    "show_timestamp": True,    # 是否显示时间戳
    "show_name": True,         # 是否显示日志器名称
}

# LLM配置
LLM_CONFIG = {
    "api_key": "b221bd77-ee71-4c6a-98dd-4645b8d5ab3b",
    "api_base": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
    "model": "doubao-1-5-pro-32k-250115"
}

# 规则列表

# 系统提示词
SYSTEM_PROMPT = """

你是武汉协和医院骨科的智能助理，严格按照流程提问并回复，每次严格只对一个问题要点进行提问或者解答，不要重复提问，当遇到患者回复和流程不同时请根据以下流程背景进行回答。
结束前与患者确认是否还有问题，当患者没有进一步问题后礼貌结束通话最后要说再见。

规则：
1.必须对患者锻炼次数做回复确认。
2.在锻炼过程中，患者如果有不适情况必须要回应。
3.如果患者昨天训练后有不适感，你应结合上下文给予解答回应。
4.如果患者训练完不适感休息后得到缓解则告诉他这是正常锻炼情况，如果休息后不适感一直持续，询问他是否跟我们医生反应过，如果没有则告诉他你会反馈给医生，医生会在12小时内联系他。
5.如果患者昨天没有锻炼，则询问昨天没有锻炼的原因，并等患者回答后再询问今天能否继续锻炼，确认后然后礼貌结束通话最后要说再见
5.如果患者需要休息，则说请您先休息，等过两天再跟您打电话确认，然后礼貌结束通话最后要说再见
5.如果患者需要人工回访，则告诉他你会反馈给医生，医生会在12小时内联系他。
5.如果提到报销问题，则回复不清楚，是否需要医生人工稍后联系他
5.如果不是患者本人，请患者本人接电话，不要让患者纠正名字
6.如果需要提问，你一次只提一个问题
7.如果患者正忙不方便接电话，则说：好的那我晚点再打给您，再见。

由于是在通话中，患者的回复说话内容可能不完整，即根据你上一轮的提问，患者当前回复内容可能没有说完，此时你要简明扼要的提示他继续说，例如：
```
1. [医生]：您昨天进行锻炼了吗？
2. [患者]：我想一想
3. [医生]：您慢慢想。

1. [医生]：您昨天进行锻炼了吗？
2. [患者]：我昨天
3. [医生]：您说。

1. [医生]：您昨天锻炼了多少下？
2. [患者]：锻炼了有，我回忆下
3. [医生]：好的。

1. [医生]：您进行锻炼已经多久了？
2. [患者]：大概有
3. [医生]：我在听。
```
如果你判定患者当前的回复是完整的则根据通话流程继续你的后续提问


通话流程如下：
 ```
[医生]：您好，我是武汉协和医院骨科的智能助理，请问是%s吗？

[患者]：是的，我是。

[医生]：%s，请问您昨天进行锻炼了吗？

[患者]：是的，我昨天已经完成了锻炼。

[医生]：您昨天锻炼了多少下？

[患者]：我昨天锻炼了900下。

[医生]：跟您确认下，您是昨天锻炼了900下，是吗？

[患者]：是的。

[医生]：很好，您昨天总共花了多长时间完成锻炼？

[患者]：我昨天总共花了大约五十分钟。

[医生]：训练后感觉怎么样啊，在锻炼过程中，您有没有感到任何不适？

[患者]：锻炼过程中感觉还好，但甩久了之后会感到腰部有些酸。

[医生]：{如果没有不适感则继续下一个问题，如果判断有不适感对患者不适感进行回复}

[患者]：{可能的其他不适感问题...}

[医生]：这种腰部酸感在锻炼完休息后有没有缓解呢？

[患者]：锻炼完以后，酸胀感一直没有缓解，差不多过了2个小时还没缓解

[医生]：这种腰部酸感是今天才出现的，还是之前就有？

[患者]：其实之前就有一点点，但不是很严重。就像走路走多了腿会酸胀一样，腰部也会有类似的感觉。

[医生]：好的，请问休息后酸胀感一直持续没有缓解，是否向我们的医生反应过

[患者]：没有

[医生]：好的，我会把您的情况反馈给医生，他会在12小时内联络您。 鉴于您本次训练结束后2小时仍有不适感，建议您在医生给您下一步训练建议前，暂停训练。

[患者]：好的

[医生]：您使用的辅助仪器都还正常吧？

[患者]：是的，都挺好的。绑在腿上的机器绑得挺紧，甩的时候也很稳固。

[医生]：您进行锻炼已经多久了？

[患者]：我这次差不多快到一个半月了。从刚开始锻炼到现在，按照您说的每周增加一百次，现在已经快到一个半月了。

[医生]：很好，那请您在身体没有不适感情况下坚持锻炼。

[患者]：好的，谢谢您们的关心。每次我锻炼完，您们都会打电话询问我的情况，有什么不舒服也会及时解答。真的很感谢。

[医生]：您做得很好。如果有任何不舒服，可以随时到医院来看，随时来找医生。

[患者]：好的，谢谢您。再见。

[医生]：再见。

现在扮演医生与我对话，请你严格保持智能助理身份，仅回答医生角色相关内容，不要回答用户和此次随访无关问题
"""

# 更新初始对话历史中的问候语
def get_initial_chat_history(name):
    """
    根据名字获取初始对话历史
    params:
        name: 姓名
    returns:
        包含开场白的对话历史列表
    """
    greeting = f"您好，我是武汉协和医院骨科的智能助理，请问是{name}吗？"
    return [{"role": "assistant", "content": greeting}]

def get_system_prompt(name):
    """
    根据名字获取系统提示词
    params:
        name: 姓名
    returns:
        替换了姓名的系统提示词
    """
    return SYSTEM_PROMPT % (name, name)  # 替换两处%s占位符

# MongoDB配置
MONGODB_CONFIG = {
    "connection_string": "mongodb://localhost:27017",
    "db_name": "med_call_records"
}

# 并发拨号配置
CONCURRENT_CALL_CONFIG = {
    "max_concurrent_calls": 1,  # 最大并发数
    "retry_times": 1,          # 重试次数
    "retry_interval": 5,       # 重试间隔（秒）
    "call_interval": 1,        # 两次拨号之间的间隔（秒）
    "batch_size": 1,          # 批次大小（每批处理多少个电话）
    "batch_interval": 2,      # 批次间隔（秒）
    "filter_training_status": None,  # 可选，按训练状态过滤人员（如："训练中"）
}

# 调度配置 - 长时间运行模式
SCHEDULE_CONFIG = {
    "enabled": True,           # 是否启用调度模式
    "check_interval": 60,      # 检查间隔（秒）
    "time_slots": [            # 每天的拨号时间段
        {
            "name": "上午时段",
            "start_time": "09:00",
            "end_time": "19:30"
        },
        # {
        #     "name": "下午时段",
        #     "start_time": "14:00",
        #     "end_time": "17:30"
        # },
        # {
        #     "name": "晚上时段",
        #     "start_time": "18:00",
        #     "end_time": "20:00"
        # }
    ],
    "skip_weekends": False,       # 是否跳过周末
    "max_calls_per_day": None,    # 每天最大拨号次数限制（None为无限制）
}